const getMetafields = async (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  admin: any,
  query: string,
  ownerType: "ORDER" | "CUSTOMER" | "DRAFTORDER",
) => {
  const response = await admin.graphql(
    `
    #graphql
    query metafields($query: String!, $ownerType: MetafieldOwnerType!) {
      metafieldDefinitions(first: 250, ownerType: $ownerType, query:$query) {
        nodes {
          id
          namespace
          key
          name
        }
      }
    }
  `,
    {
      variables: {
        query,
        ownerType,
      },
    },
  );

  const responseJson = await response.json();

  return responseJson.data.metafieldDefinitions.nodes;
};

const createMetafieldDefinition = async (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  admin: any,
  definition: {
    name: string;
    key: string;
    namespace: string;
    type: string;
    description: string;
    ownerType: string;
  },
) => {
  const definitionData = {
    name: definition.name,
    key: definition.key,
    // namespace: definition.namespace,
    type: definition.type,
    description: definition.description,
    ownerType: definition.ownerType,
    access: {
      admin: "MERCHANT_READ",
      storefront: "PUBLIC_READ",
      customerAccount: "READ",
    },
    pin: true,
  };

  const response = await admin.graphql(
    `
    #graphql
    mutation CreateCustomerMetafieldDefinition($definition: MetafieldDefinitionInput!) {
      metafieldDefinitionCreate(definition: $definition) {
        createdDefinition {
          id
          namespace
          key
          ownerType
          name
        }
        userErrors {
          field
          message
        }
      }
    }
  `,
    {
      variables: {
        definition: definitionData,
      },
    },
  );

  const responseJson = await response.json();

  return responseJson.data.metafieldDefinitionCreate;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const initMetafields = async (admin: any) => {
  const appResponse = await admin.graphql(
    `
    #graphql
    query getApp {
      app {
        id
      }
    }
  `,
  );

  const appResponseJson = await appResponse.json();

  const appId = appResponseJson.data?.app?.id?.replace("gid://shopify/App/", "");
  const query = `namespace:app--${appId}*`;

  const draftOrdersMetafields = await getMetafields(admin, query, "DRAFTORDER");
  const DRAFTORDER_METAFIELDS = [
    {
      name: "Location ID",
      namespace: "store",
      key: "location_id",
      type: "single_line_text_field",
      description: "Location ID of the POS machine",
      ownerType: "DRAFTORDER",
    },
    {
      name: "Staff ID",
      namespace: "staff",
      key: "staff_id",
      type: "single_line_text_field",
      description: "ID of staff who processed the order",
      ownerType: "DRAFTORDER",
    },
    {
      name: "Order Date",
      namespace: "order",
      key: "order_date",
      type: "date_time",
      description: "Exact date and time when the order was processed",
      ownerType: "DRAFTORDER",
    },
    {
      name: "Order ID",
      namespace: "order",
      key: "order_id",
      type: "single_line_text_field",
      description: "ID of the order processed",
      ownerType: "DRAFTORDER",
    },
    {
      name: "Invoice",
      namespace: "order",
      key: "invoice",
      type: "single_line_text_field",
      description: "Invoice ID or reference",
      ownerType: "DRAFTORDER",
    },
    {
      name: "Total Order Amount",
      namespace: "order",
      key: "total_order_amount",
      type: "number_decimal",
      description: "Total order amount captured by POS machine",
      ownerType: "DRAFTORDER",
    },
    {
      name: "Actual Order Amount",
      namespace: "calc",
      key: "actual_order_amount",
      type: "number_decimal",
      description: "Actual order amount",
      ownerType: "DRAFTORDER",
    },
    {
      name: "Discount Amount",
      namespace: "calc",
      key: "discount_amount",
      type: "number_decimal",
      description: "Discount amount calculated by POS machine",
      ownerType: "DRAFTORDER",
    },
  ];
  const missingDraftOrdersMetafields = DRAFTORDER_METAFIELDS.filter((metafield) => {
    const existingMetafield = draftOrdersMetafields.find(
      (metafieldNode: { key: string }) => metafieldNode.key === metafield.key,
    );
    return !existingMetafield;
  });

  if (missingDraftOrdersMetafields.length > 0) {
    missingDraftOrdersMetafields.forEach(async (metafield) => {
      await createMetafieldDefinition(admin, {
        description: metafield.description,
        key: metafield.key,
        name: metafield.name,
        namespace: `$app:${metafield.namespace}`,
        ownerType: "DRAFTORDER",
        type: metafield.type,
      });
    });
  }

  const ordersMetafields = await getMetafields(admin, query, "ORDER");
  const ORDER_METAFIELDS = [
    {
      name: "Location ID",
      namespace: "store",
      key: "location_id",
      type: "single_line_text_field",
      description: "Location ID of the POS machine",
      ownerType: "ORDER",
    },
    {
      name: "Staff ID",
      namespace: "staff",
      key: "staff_id",
      type: "single_line_text_field",
      description: "ID of staff who processed the order",
      ownerType: "ORDER",
    },
    {
      name: "Order Date",
      namespace: "order",
      key: "order_date",
      type: "date_time",
      description: "Exact date and time when the order was processed",
      ownerType: "ORDER",
    },
    {
      name: "Order ID",
      namespace: "order",
      key: "order_id",
      type: "single_line_text_field",
      description: "ID of the order processed",
      ownerType: "ORDER",
    },
    {
      name: "Invoice",
      namespace: "order",
      key: "invoice",
      type: "single_line_text_field",
      description: "Invoice ID or reference",
      ownerType: "ORDER",
    },
    {
      name: "Total Order Amount",
      namespace: "order",
      key: "total_order_amount",
      type: "number_decimal",
      description: "Total order amount captured by POS machine",
      ownerType: "ORDER",
    },
    {
      name: "Actual Order Amount",
      namespace: "calc",
      key: "actual_order_amount",
      type: "number_decimal",
      description: "Actual order amount",
      ownerType: "ORDER",
    },
    {
      name: "Discount Amount",
      namespace: "calc",
      key: "discount_amount",
      type: "number_decimal",
      description: "Discount amount calculated by POS machine",
      ownerType: "ORDER",
    },
  ];

  const missingOrdersMetafields = ORDER_METAFIELDS.filter((metafield) => {
    const existingMetafield = ordersMetafields.find(
      (metafieldNode: { key: string }) => metafieldNode.key === metafield.key,
    );
    return !existingMetafield;
  });

  if (missingOrdersMetafields.length > 0) {
    missingOrdersMetafields.forEach(async (metafield) => {
      await createMetafieldDefinition(admin, {
        description: metafield.description,
        key: metafield.key,
        name: metafield.name,
        namespace: `$app:${metafield.namespace}`,
        ownerType: "ORDER",
        type: metafield.type,
      });
    });
  }

  const customersMetafields = await getMetafields(admin, query, "CUSTOMER");
  const CUSTOMER_METAFIELDS = [
    {
      name: "Birth Date",
      namespace: "customer",
      key: "birth_date",
      type: "date",
      description: "The customer's date of birth",
      ownerType: "CUSTOMER",
    },
    {
      name: "Gender",
      namespace: "customer",
      key: "gender",
      type: "single_line_text_field",
      description: "The customer's gender",
      ownerType: "CUSTOMER",
    },
    {
      name: "Register Date",
      namespace: "customer",
      key: "register_date",
      type: "date_time",
      description: "The date the customer registered",
      ownerType: "CUSTOMER",
    },
    {
      name: "Register Employee",
      namespace: "customer",
      key: "register_employee",
      type: "single_line_text_field",
      description: "The employee who registered the customer",
      ownerType: "CUSTOMER",
    },
    {
      name: "Register Location",
      namespace: "customer",
      key: "register_location",
      type: "single_line_text_field",
      description: "The location where the customer was registered",
      ownerType: "CUSTOMER",
    },
    {
      name: "Status",
      namespace: "customer",
      key: "status",
      type: "single_line_text_field",
      description: "The status of the customer",
      ownerType: "CUSTOMER",
    },
    {
      name: "Points",
      namespace: "customer",
      key: "points",
      type: "number_integer",
      description: "The points of the customer",
      ownerType: "CUSTOMER",
    },
    {
      name: "Store Credit",
      namespace: "customer",
      key: "store_credit",
      type: "number_decimal",
      description: "The store credit of the customer",
      ownerType: "CUSTOMER",
    },
  ];

  const missingCustomerMetafields = CUSTOMER_METAFIELDS.filter(
    (customerMetafield) =>
      !customersMetafields.some(
        (metafield: { key: string }) => metafield.key === customerMetafield.key,
      ),
  );

  if (missingCustomerMetafields.length > 0) {
    missingCustomerMetafields.forEach(async (metafield) => {
      await createMetafieldDefinition(admin, {
        description: metafield.description,
        key: metafield.key,
        name: metafield.name,
        namespace: `$app:${metafield.namespace}`,
        ownerType: "CUSTOMER",
        type: metafield.type,
      });
    });
  }
};
