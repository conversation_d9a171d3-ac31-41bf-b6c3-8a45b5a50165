import {LoaderFunctionArgs} from "@remix-run/node";
import {use<PERSON><PERSON><PERSON>, use<PERSON>oaderD<PERSON>, useNavigate, useSearchParams} from '@remix-run/react';
import {
  BlockStack,
  Button,
  Card,
  Checkbox,
  InlineGrid,
  InlineStack,
  Layout,
  List,
  Page,
  Text,
  TextField,
} from "@shopify/polaris";
import {useCallback, useState} from "react";

import db from "../../db.server";
import responseBadRequest from '../../utils/response.badRequest';
import EditAmountOffModal from './edit_modal';
import {useAppBridge} from '@shopify/app-bridge-react';

export const loader = async ({ params }: LoaderFunctionArgs) => {
  const { id: rewardId } = params;

  if (!rewardId) {
    throw responseBadRequest("Reward ID is required");
  }

  else if (rewardId === "new") {
    return {
      id: null,
      birthdayRewardType: "AMOUNT_OFF",
      title: "",
      expiryMonths: null,
      rewardValue: 0,
      discountType: "FIXED",
      minimumRequirement: "NONE",
      minimumValue: 0,
      productDiscounts: false,
      orderDiscounts: false,
      shippingDiscounts: false,
    };
  }

  else if (isNaN(Number(rewardId))) {
    throw responseBadRequest("Reward ID is not a number");
  }

  else {
    // Get reward details from local database
    const reward = await db.birthdayReward.findFirstOrThrow({
      where: {
        id: Number(rewardId),
      },
      select: {
        id: true,
        birthdayRewardType: true,
        title: true,
        expiryMonths: true,
        rewardValue: true,
        discountType: true,
        minimumRequirement: true,
        minimumValue: true,
        productDiscounts: true,
        orderDiscounts: true,
        shippingDiscounts: true,
      },
    });
    return reward;
  }
}

export const action = async ({ request }: LoaderFunctionArgs) => {
  if (request.method === "POST") {
    const { settingsId, title, rewardValue, expiryMonths, discountType, minimumRequirement, minimumValue, combinations } = await request.json();
    console.log("Submitted data:", { settingsId, title, rewardValue, expiryMonths });

    // Create the birthday reward
    const birthdayReward = await db.birthdayReward.create({
      data: {
        settingsId: Number(settingsId),
        birthdayRewardType: "AMOUNT_OFF",
        title: title,
        expiryMonths: expiryMonths ? Number(expiryMonths) : undefined,
        rewardValue: Number(rewardValue),
        discountType: discountType.toUpperCase(),
        minimumRequirement: minimumRequirement.toUpperCase(),
        minimumValue: minimumValue,
        productDiscounts: combinations.product,
        orderDiscounts: combinations.order,
        shippingDiscounts: combinations.shipping,
      },
      select: {
        id: true,
        birthdayRewardType: true,
        title: true,
        expiryMonths: true,
        rewardValue: true,
        discountType: true,
        minimumRequirement: true,
        minimumValue: true,
        productDiscounts: true,
        orderDiscounts: true,
        shippingDiscounts: true,
      },
    });
    return birthdayReward;
  }

  else if (request.method === "PATCH") {
    const { id, title, rewardValue, expiryMonths } = await request.json();

    // Update the birthday reward
    const birthdayReward = await db.birthdayReward.update({
      where: {
        id: Number(id),
      },
      data: {
        title: title,
        expiryMonths: expiryMonths ? Number(expiryMonths) : undefined,
        rewardValue: Number(rewardValue),
      },
      select: {
        id: true,
        birthdayRewardType: true,
        title: true,
        expiryMonths: true,
        rewardValue: true,
      },
    });
    return birthdayReward;
  }

  else if (request.method === "DELETE") {
    const { id } = await request.json();

    // Delete the birthday reward
    const birthdayReward = await db.birthdayReward.delete({
      where: {
        id: Number(id),
      },
      select: {
        id: true,
        birthdayRewardType: true,
        title: true,
        expiryMonths: true,
        rewardValue: true,
      },
    });
    return birthdayReward;
  }

  else {
    throw responseBadRequest("Invalid request method");
  }
}

export default function PointsRewardBirthday() {
  const data = useLoaderData<typeof loader>();
  const [searchParams] = useSearchParams();
  const fetcher = useFetcher();

  const [hasDifferentTiers, setHasDifferentTiers] = useState<boolean>(false);
  const [hasDiscount, setHasDiscount] = useState<boolean>(!!data.id);

  const [title, setTitle] = useState<string>(data.title);
  const [isExpiryEnabled, setIsExpiryEnabled] = useState<boolean>(!!data.expiryMonths);
  const [expiryMonths, setExpiryMonths] = useState<string>(String(data.expiryMonths ?? 0));

  const [rewardValue, setRewardValue] = useState<number>(data.rewardValue ?? 0);
  const [discountType, setDiscountType] = useState<"percentage" | "fixed">(
    data.discountType === "PERCENTAGE" || data.discountType === "FIXED"
      ? (data.discountType.toLowerCase() as "percentage" | "fixed")
      : "fixed"
  );
  const [minimumRequirement, setMinimumRequirement] = useState<"none" | "amount" | "quantity">(
    data.minimumRequirement === "NONE" || data.minimumRequirement === "AMOUNT" || data.minimumRequirement === "QUANTITY"
      ? data.minimumRequirement.toLowerCase() as "none" | "amount" | "quantity"
      : "none"
  );
  const [minimumValue, setMinimumValue] = useState<number | null>(data.minimumValue);
  const [combinations, setCombinations] = useState<{
    product: boolean;
    order: boolean;
    shipping: boolean;
  }>({
    product: data.productDiscounts || false,
    order: data.orderDiscounts || false,
    shipping: data.shippingDiscounts || false,
  });

  const navigate = useNavigate();
  const settingsId = searchParams.get('settingsId');
  const shopify = useAppBridge();

  const EDIT_AMOUNT_OFF_MODAL_ID = "edit-amount-off-modal";

  const handleEditModalOpen = useCallback(() => {
    shopify?.modal?.show(EDIT_AMOUNT_OFF_MODAL_ID);
  }, [shopify]);

  const handleSaveReward = useCallback(async () => {
    // Update existing reward
    if (hasDiscount) {
      fetcher.submit(
        JSON.stringify({
          id: data.id,
          settingsId: settingsId,
          title: title,
          expiryMonths: isExpiryEnabled ? expiryMonths : undefined,
          rewardValue: rewardValue,
          minimumRequirement: minimumRequirement,
          discountType: discountType,
          combinations: combinations,
        }),
        {
          method: data.id ? "PATCH" : "POST",
          encType: "application/json",
        }
      );
    }
    navigate(`/app/loyalties/birthday/`);
  }, [hasDiscount, data.id, fetcher, settingsId, title, isExpiryEnabled, expiryMonths, navigate, combinations, discountType, minimumRequirement, rewardValue]);

  const handleDeleteReward = useCallback(() => {
    // Delete existing reward
    fetcher.submit(
      {},
      {
        method: "DELETE",
        encType: "application/json",
      }
    );
    navigate(`/app/loyalties/birthday/`);
  }, [fetcher, navigate]);

  return (
    <Page title="Amount off order">
      <Layout>
        <InlineGrid columns={["twoThirds","oneThird"]}>
          <Layout.Section>
            <div style={{ marginTop: "1rem" }}>
              <Card>
                <TextField
                  label={
                    <Text as="h2" variant="headingMd">
                      Title
                    </Text>
                  }
                  value={title}
                  onChange={setTitle}
                  autoComplete="off"
                  helpText="Customers will see this in their cart and at checkout."
                />
              </Card>
            </div>

            <div style={{ marginTop: "1rem" }}>
              <Card>
                <BlockStack gap="200">
                  <Text as="h2" variant="headingMd">
                    Reward value
                  </Text>
                  <Checkbox
                    label="Different point values based on VIP tiers"
                    checked={hasDifferentTiers}
                    onChange={setHasDifferentTiers}
                  />
                  <Card>
                    <InlineStack align="space-between" gap="200">
                      <BlockStack gap="100" align="center">
                        {data.id || hasDiscount ? 
                          <>
                            <Text as="p" fontWeight="bold">Amount off order</Text>
                            <Text as="p" tone="subdued">
                              {data.discountType === 'FIXED' && '$'}
                              {data.rewardValue}
                              {data.discountType === 'PERCENTAGE' && '%'} off entire order
                            </Text>
                          </> : 
                          <Text as="p" tone="subdued">No discount</Text>
                        }
                      </BlockStack>
                      <Button 
                        variant={data.id || hasDiscount ? "plain" : "primary"} 
                        onClick={handleEditModalOpen}
                      >
                        {data.id || hasDiscount ? "Edit" : "Create"}
                      </Button>
                    </InlineStack>
                  </Card>
                </BlockStack>
              </Card>
            </div>
          </Layout.Section>

          <Layout.Section>
            <div style={{ marginTop: "1rem" }}>
              <Card>
                <Text as="h2" variant="headingMd">
                  Summary
                </Text>
                <div style={{ marginTop: "0.5rem" }}>
                  <List>
                    <List.Item>Point</List.Item>
                    <List.Item>Applies to all order</List.Item>
                  </List>
                </div>
              </Card>
            </div>

            <div style={{ marginTop: "1rem" }}>
              <Card>
                <InlineStack align="space-between" blockAlign="center">
                  <Text as="h2" variant="headingMd">Reward Expiry</Text>
                  <Button
                    pressed={isExpiryEnabled}
                    onClick={() => setIsExpiryEnabled(!isExpiryEnabled)}
                   tone={isExpiryEnabled ? "critical" : "success"}
                  >
                    {isExpiryEnabled ? 'Enabled' : 'Disabled'}
                  </Button>
                </InlineStack>
                {isExpiryEnabled ? (
                  <TextField
                    label="Expiry months"
                    value={expiryMonths}
                    onChange={setExpiryMonths}
                    type="number"
                    suffix="month(s)"
                    autoComplete="off"
                  />
                ) : (
                  <Text tone="subdued" as="p">
                    No expiry now. Turn on the switch to set expiry date.
                  </Text>
                )}
              </Card>
            </div>
          </Layout.Section>
        </InlineGrid>

        <Layout.Section>
          <InlineStack gap="600" align="space-between">
            { data.id ?
              <Button size="large" variant="primary" tone="critical" onClick={handleDeleteReward}>
                Delete
              </Button> :
              <BlockStack />
            }
            <Button size="large" variant="primary" onClick={handleSaveReward}>
              Save
            </Button>
          </InlineStack>
        </Layout.Section>
      </Layout>

      <EditAmountOffModal
        id={EDIT_AMOUNT_OFF_MODAL_ID}
        rewardValue={rewardValue}
        setRewardValue={setRewardValue}
        discountType={discountType}
        setDiscountType={setDiscountType}
        minimumRequirement={minimumRequirement}
        setMinimumRequirement={setMinimumRequirement}
        minimumValue={minimumValue}
        setMinimumValue={setMinimumValue}
        combinations={combinations}
        setCombinations={setCombinations}
        setHasDiscount={setHasDiscount}
      />
    </Page>
  );
}
