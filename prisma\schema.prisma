// Configuration for Prisma Client generation
generator client {
  provider = "prisma-client-js"
}

// Database connection configuration
datasource db {
  provider = "sqlite"
  url      = "file:dev.sqlite"
}

// Stores Shopify session data for authenticated users and API access
model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      <PERSON>   @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  Boolean   @default(false)
  locale        String?
  collaborator  <PERSON><PERSON><PERSON>?  @default(false)
  emailVerified Boolean?  @default(false)
}

// Tracks membership cancellations with customer information and cancellation dates
model MembershipCancel {
  id         Int      @id @default(autoincrement())
  customerId String
  cellphone  String
  cancelDate DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

// Stores Shopify store information and authentication details
model Shop {
  id                      Int                       @id @default(autoincrement())
  shopId                  String
  shopName                String
  myshopifyDomain         String
  shopToken               String?
  apiKey                  String?
  loyaltyPrograms         LoyaltyProgram[]
  // Tracks sales orders with mapping between POS and Shopify order IDs
  shopLoyaltyProgram      ShopCreditLoyaltyProgram?
  // Added inverse relation field for SignupProgramSettings
  birthdaySettings        BirthdayProgramSettings?
  WaysEarnReward          WaysEarnReward[]
  completeProfileSettings CompleteProfileSettings?
  createdAt               DateTime                  @default(now())
  updatedAt               DateTime                  @updatedAt
}

model ShopCreditLoyaltyProgram {
  id           Int      @id @default(autoincrement())
  shopId       Int      @unique
  shop         Shop     @relation(fields: [shopId], references: [id])
  isActive     Boolean  @default(false)
  singularName String
  pluralName   String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

model SalesOrders {
  id         Int            @id @default(autoincrement())
  orderPosId String         @unique
  ordShopiId String         @unique
  createdAt  DateTime       @default(now())
  returnOrd  ReturnOrders[]

  @@index([orderPosId])
}

// Manages return orders linked to original sales orders
model ReturnOrders {
  id           Int         @id @default(autoincrement())
  orderId      String      @unique
  retOrdShopId String      @unique
  salesOrdId   Int
  salesOrder   SalesOrders @relation(fields: [salesOrdId], references: [id])
}

// Main loyalty program model that connects all loyalty features for a shop
model LoyaltyProgram {
  id           Int                  @id @default(autoincrement())
  shop         Shop                 @relation(fields: [shopId], references: [id])
  shopId       Int
  isActive     Boolean              @default(true)
  programType  LoyaltyProgramType
  createdAt    DateTime             @default(now())
  updatedAt    DateTime             @updatedAt
  points       LoyaltyPoints?
  vipSettings  LoyaltyVIPSettings?
  vipTiers     LoyaltyVIPTier[]
  storeCredits LoyaltyStoreCredit[]
  referrals    LoyaltyReferral[]

  @@unique([shopId, programType])
}

// Manages points-based loyalty program settings and configuration
model LoyaltyPoints {
  id                          Int             @id @default(autoincrement())
  loyaltyProgram              LoyaltyProgram  @relation(fields: [loyaltyProgramId], references: [id])
  loyaltyProgramId            Int             @unique
  programName                 String
  pointSingular               String          @default("Point")
  pointPlural                 String          @default("Points")
  pointsPerCurrency           Float           @default(1.0)
  currencyAmount              Float           @default(1.0)
  pointsRedemptionAmount      Float           @default(100) //Point
  pointsRedemptionValue       Float           @default(0.01) // $
  maxRedeemPercentage         Int             @default(30)
  minPurchaseAmount           Float?
  roundingMethod              String?
  isConvertPointsByPercentage Boolean?        @default(false)
  // Order total calculation
  includeProductTotal         Boolean         @default(true)
  includeShipping             Boolean         @default(false)
  includeTaxes                Boolean         @default(false)
  // Points issue settings
  pointsIssueType             PointsIssueType @default(DELAYED)
  issueDays                   Int             @default(14)
  orderStatus                 OrderStatus     @default(PAID_FULFILLED)
  // Refund handling
  orderRefundType             RefundType      @default(PROPORTIONAL)
  redeemedRefundType          RefundType      @default(PROPORTIONAL)
  createdAt                   DateTime        @default(now())
  updatedAt                   DateTime        @updatedAt
}

// Stores VIP Program Settings
model LoyaltyVIPSettings {
  id               Int            @id @default(autoincrement())
  loyaltyProgram   LoyaltyProgram @relation(fields: [loyaltyProgramId], references: [id])
  loyaltyProgramId Int            @unique
  entryMethod      String         @default("points") // "points" or "amount"
  ordersCount      Boolean        @default(false)
  validationType   String         @default("immediately") // "immediately" or "days_after_paid"
  validationDays   Int            @default(0)
  expirationType   String         @default("lifetime") // "lifetime" or "period_of_time"
  expirationDays   Int            @default(0)
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
}

// Defines VIP tiers with requirements and benefits for the loyalty program
model LoyaltyVIPTier {
  id                 Int                  @id @default(autoincrement())
  loyaltyProgram     LoyaltyProgram       @relation(fields: [loyaltyProgramId], references: [id])
  loyaltyProgramId   Int
  name               String
  spendRequirement   Float?
  pointsRequirement  Int?
  spendAmount        Int?
  pointEarn          Int?
  rewards            LoyaltyVIPReward[] // Relation to rewards
  PlaceAnOrderReward PlaceAnOrderReward[]
  basedOnDiffTier    Boolean              @default(false)
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
}

// Reward type options for VIP tier rewards
enum RewardType {
  POINTS
  STORE_CREDIT
  AMOUNT_OFF
  FREE_SHIPPING
}

// Discount type options for amount-off rewards
enum DiscountType {
  PERCENTAGE
  FIXED
}

// Minimum requirement type options for amount-off and free-shipping rewards
enum RequirementType {
  NONE
  AMOUNT
  QUANTITY
}

// Defines rewards for VIP tiers
model LoyaltyVIPReward {
  id                 Int              @id @default(autoincrement())
  vipTier            LoyaltyVIPTier   @relation(fields: [vipTierId], references: [id], onDelete: Cascade)
  vipTierId          Int
  title              String
  rewardType         RewardType // Enum for reward types
  value              String? // Value for points, store-credit, amount-off
  discountType       DiscountType? // Enum for discount types
  minimumRequirement RequirementType? // Enum for minimum requirement types
  minimumValue       Float? // Value for minimum requirement
  // Combinations for amount-off rewards
  productDiscounts   Boolean?
  orderDiscounts     Boolean?
  shippingDiscounts  Boolean?
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
}

// Manages store credit balances for customers in the loyalty program
model LoyaltyStoreCredit {
  id                Int            @id @default(autoincrement())
  loyaltyProgram    LoyaltyProgram @relation(fields: [loyaltyProgramId], references: [id])
  loyaltyProgramId  Int
  customerLoyaltyId Int            @unique
  balance           Float          @default(0)
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
}

// Tracks customer referrals and associated rewards in the loyalty program
model LoyaltyReferral {
  id                 Int            @id @default(autoincrement())
  loyaltyProgram     LoyaltyProgram @relation(fields: [loyaltyProgramId], references: [id])
  loyaltyProgramId   Int
  referrerCustomerId Int
  referredCustomerId Int
  status             ReferralStatus @default(PENDING)
  pointsAwarded      Int?
  storeCreditAwarded Float?
  createdAt          DateTime       @default(now())
  updatedAt          DateTime       @updatedAt
}

// Types of transactions for points and store credits
enum TransactionType {
  EARN
  REDEEM
  EXPIRE
  ADJUST
  REFUND
}

// Status options for customer referrals
enum ReferralStatus {
  PENDING
  COMPLETED
  CANCELLED
}

// Types of loyalty programs that can be configured
enum LoyaltyProgramType {
  POINTS
  VIP_TIER
  STORE_CREDIT
  REFERRAL
}

// Status options for loyalty program features
enum FeatureStatus {
  ACTIVE
  INACTIVE
  PAUSED
  SCHEDULED
  TESTING
  ARCHIVED
}

// Points issue type options for order rewards
enum PointsIssueType {
  IMMEDIATE
  DELAYED
}

// Order status options for delayed points issuance
enum OrderStatus {
  PAID_FULFILLED
  PAID
}

// Refund handling type options
enum RefundType {
  PROPORTIONAL
  FULL
  NONE
}

enum SignupRewardType {
  POINTS
  STORE_CREDIT
  AMOUNT_OFF
  FREE_SHIPPING
}

enum PlaceAnOrderRewardType {
  POINTS_REWARD
  STORE_CREDIT
}

enum AmountOffType {
  PERCENTAGE
  FIXED_AMOUNT
}

enum MinRequirementType {
  NO_MINIMUM
  MIN_PURCHASE_AMOUNT
  MIN_QUANTITY_ITEMS
}

enum DiscountCombinationType {
  PRODUCT_DISCOUNTS
  ORDER_DISCOUNTS
  SHIPPING_DISCOUNTS
}

// Ways to earn reward

//Enum for different types of rewards
enum WaysEarnRewardType {
  SIGN_UP
  REFERRAL
  CUSTOM_REWARD
  COMPLETE_PROFILE
  PURCHASE
  CELEBRATE_BIRTHDAY
}

model DefaultWaysEarnRewardType {
  id        Int                @id @default(autoincrement())
  code      WaysEarnRewardType @unique // "PURCHASE", "SIGN_UP", ...
  ways      WaysEarnReward[]
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt
}

model WaysEarnReward {
  id                          Int                        @id @default(autoincrement())
  typeEarnReward              WaysEarnRewardType
  title                       String
  subtitle                    String?
  isActive                    Boolean                    @default(true)
  Shop                        Shop?                      @relation(fields: [shopId], references: [id])
  shopId                      Int?
  DefaultWaysEarnRewardType   DefaultWaysEarnRewardType? @relation(fields: [defaultWaysEarnRewardTypeId], references: [id])
  defaultWaysEarnRewardTypeId Int?
  rewards                     SignupReward[] // Relation to signup rewards
  purchaseRewards             PlaceAnOrderReward[] // Relation to place an order rewards
  createdAt                   DateTime                   @default(now())
  updatedAt                   DateTime                   @updatedAt

  @@unique([shopId, defaultWaysEarnRewardTypeId])
}

enum BirthdayRewardType {
  POINTS       @map("points")
  STORE_CREDIT @map("store-credit")
  AMOUNT_OFF   @map("amount-off")
}

model BirthdayProgramSettings {
  id        Int              @id @default(autoincrement())
  shopId    Int              @unique
  shop      Shop             @relation(fields: [shopId], references: [id])
  pageTitle String           @default("Celebrate a birthday")
  isActive  Boolean          @default(true)
  rewards   BirthdayReward[]
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
}

model BirthdayReward {
  id                 Int                     @id @default(autoincrement())
  settingsId         Int
  settings           BirthdayProgramSettings @relation(fields: [settingsId], references: [id])
  birthdayRewardType BirthdayRewardType
  title              String
  expiryMonths       Int?
  rewardValue        Float?
  discountType       DiscountType?
  minimumRequirement RequirementType?
  minimumValue       Float?
  productDiscounts   Boolean?
  orderDiscounts     Boolean?
  shippingDiscounts  Boolean?
  createdAt          DateTime                @default(now())
  updatedAt          DateTime                @updatedAt
}

// Defines rewards for signup program
model SignupReward {
  id                 Int              @id @default(autoincrement())
  waysEarnReward     WaysEarnReward?  @relation(fields: [waysEarnRewardId], references: [id], onDelete: Cascade)
  waysEarnRewardId   Int?
  title              String
  rewardType         RewardType // Enum for reward types
  value              String // Value for points, store-credit, amount-off
  discountType       DiscountType? // Enum for discount types (for amount-off rewards)
  minimumRequirement RequirementType? // Enum for minimum requirement types
  minimumValue       Float? // Value for minimum requirement
  // Combinations for amount-off rewards
  productDiscounts   Boolean?
  orderDiscounts     Boolean?
  shippingDiscounts  Boolean?
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
}

model CompleteProfileReward {
  id                        Int                      @id @default(autoincrement())
  title                     String
  type                      RewardType // Enum for reward types
  value                     String? // Value for points, store-credit, amount-off
  discountType              DiscountType? // Enum for discount types (for amount-off rewards)
  minRequirementType        MinRequirementType? // Enum for minimum requirement types
  minRequirementValue       Float? // Value for minimum requirement
  // Combinations for amount-off rewards
  productDiscounts          Boolean?
  orderDiscounts            Boolean?
  shippingDiscounts         Boolean?
  CompleteProfileSettings   CompleteProfileSettings? @relation(fields: [completeProfileSettingsId], references: [id])
  completeProfileSettingsId Int?
  createdAt                 DateTime                 @default(now())
  updatedAt                 DateTime                 @updatedAt
}

model Gender {
  id              Int           @id @default(autoincrement())
  name            String
  customProfile   CustomProfile @relation(fields: [customProfileId], references: [id])
  customProfileId Int
}

model CustomProfile {
  id                      Int                      @id @default(autoincrement())
  editable                Boolean                  @default(true)
  gender                  Gender[]
  completeProfileSettings CompleteProfileSettings?
  createdAt               DateTime                 @default(now())
  updatedAt               DateTime                 @updatedAt
}

model CompleteProfileSettings {
  id              Int                     @id @default(autoincrement())
  shopId          Int                     @unique
  shop            Shop                    @relation(fields: [shopId], references: [id])
  pageTitle       String                  @default("Complete your profile")
  isActive        Boolean                 @default(true)
  rewards         CompleteProfileReward[]
  customProfileId Int?                    @unique
  customProfile   CustomProfile?          @relation(fields: [customProfileId], references: [id])
  createdAt       DateTime                @default(now())
  updatedAt       DateTime                @updatedAt
}

// Defines rewards for place an order program
model PlaceAnOrderReward {
  id                 Int              @id @default(autoincrement())
  waysEarnReward     WaysEarnReward   @relation(fields: [waysEarnRewardId], references: [id], onDelete: Cascade)
  waysEarnRewardId   Int
  title              String
  rewardType         RewardType // Enum for reward types
  value              String // Value for points, store-credit, amount-off
  discountType       DiscountType? // Enum for discount types (for amount-off rewards)
  minimumRequirement RequirementType? // Enum for minimum requirement types
  minimumValue       Float? // Value for minimum requirement
  // Combinations for amount-off rewards
  productDiscounts   Boolean?
  orderDiscounts     Boolean?
  shippingDiscounts  Boolean?
  LoyaltyVIPTier     LoyaltyVIPTier?  @relation(fields: [loyaltyVIPTierId], references: [id])
  loyaltyVIPTierId   Int?
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
}

// Save history Point Order
model LoyaltyPointsHistory {
  id         Int       @id @default(autoincrement())
  customerId String
  orderId    String
  points     Int
  earnedAt   DateTime
  issueAt    DateTime
  status     String // "PENDING" | "ISSUED"
  issuedAt   DateTime?
  createdAt  DateTime?
}
