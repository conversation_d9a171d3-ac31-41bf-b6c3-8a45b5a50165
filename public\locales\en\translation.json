{"greeting": "Hello", "locale": "en-US", "placeOrder": {"pageTitle": "Place an order", "backAction": "Loyalties", "orderSettings": "Order Settings", "title": "Title", "titleValue": "Place an order", "description": "Description", "descriptionValue": "Customers will see this in their cart and at checkout.", "rewards": "Rewards", "rewardsDescription": "Customers earn rewards by placing an order.", "edit": "Edit", "addNew": "Add new", "save": "Save", "pointsReward": {"title": "Points reward", "description": "From 1 point for every $1 spent"}, "creditReward": {"title": "Store credit reward", "description": "From 3% store credits"}, "editInPoints": "Edit in Points", "tierStarter": "Starter"}, "navigation": {"home": "Home", "members": "Members", "loyalties": "Loyalties", "settings": "Settings", "referrals": "Referrals"}, "customReward": {"title": "Custom reward", "pageTitle": "Custom reward", "backAction": "Loyalties", "status": {"title": "Status", "active": "Active", "on": "On", "pauseInfo": "Turn \"Off\" the switch to pause."}, "rewardType": {"title": "Reward Type", "discountCode": "Discount Code", "custom": "Custom"}, "description": {"title": "Description", "helpText": "Enter a description of the reward customers will earn."}, "discountCode": {"title": "Discount Code", "select": "Select"}, "conditions": {"title": "Conditions", "helpText": "Enter details about this reward. (Optional)"}, "summary": {"title": "Summary"}, "icon": {"title": "Icon", "defaultIcon": "De<PERSON>ult <PERSON>", "customIcon": "Custom Icon", "upload": "Upload", "sizeInfo": "1:1 ratio (e.g. 60×60px) and less than 50KB png, jpg"}, "titleField": {"title": "Title", "helpText": "Enter reward title."}, "buttons": {"delete": "Delete", "save": "Save"}}, "loyalty": {"title": "Loyalty", "waysToEarnReward": "Ways to earn reward", "setWaysToEarnRewards": "Set ways for your customers to earn rewards.", "selectWaysToEarn": "Select ways to earn", "customizeEarnPoints": "Customize how your customer can earn points…", "continue": "Continue", "status": {"active": "Active", "inactive": "Inactive"}}, "common": {"add": "Add", "close": "Close", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "spent": "Spent", "orders": "orders", "update": "Update", "editValue": "Edit Value", "from": "From"}, "home": {"welcome": "Welcome to OMO Sync", "subtitle": "Streamline your online and offline store operations and boost sales with our powerful tools", "keyFeatures": "Key Features", "needHelp": "Need Help?", "contactInfo": "Contact our specialists for assistance at", "features": {"memberSync": {"buttonText": "Connect POS System", "title": "Sync Member Data", "description": "Sync member data between Shopify and physical stores. Supports member lookup, editing, deletion, and discount inquiry/redemption features"}, "orderSync": {"buttonText": "Connect POS System", "title": "Sync Order Data", "description": "Sync physical store orders to Shopify admin to centrally manage sales data"}, "membership": {"buttonText": "Coming Soon", "title": "Integrate Membership System", "description": "Easily create a cross-channel membership system, offering exclusive discounts based on membership tiers to effectively boost customer loyalty"}}}, "loyalties": {"title": "Loyalties", "backToPoints": "Points", "programTypes": {"points": "Points", "storeCredit": "Store Credit", "vip": "VIP Status"}, "rewards": {"addNewReward": "Add New Reward", "editReward": "<PERSON> <PERSON><PERSON>", "points": "Points", "storeCredit": "Store Credit", "amountOff": "Amount Off Order", "freeShipping": "Free Shipping", "pointReward": "Point reward", "editPointReward": "Edit Point Reward", "rewardTitle": "Reward Title", "rewardValue": "Reward Value", "pointsText": "points", "storeCreditsText": "store credits", "offOrderText": "off order", "freeShippingText": "Free shipping", "editStoreCreditReward": "Edit Store Credit Reward", "storeCreditReward": "Store Credit Reward", "rewardTitleDescription": "Enter a title for this reward that will be displayed to customers.", "rewardValueDescription": "Enter the amount of store credit customers will receive.", "editAmountOffOrderReward": "Edit Amount Off Order Reward", "amountOffOrderReward": "Amount Off Order Reward", "percentage": "Percentage", "fixedAmount": "Fixed Amount", "minimumRequirements": "Minimum Requirements", "noMinimumRequirements": "No minimum requirements", "minimumPurchaseAmount": "Minimum purchase amount", "minimumQuantityItems": "Minimum quantity of items", "minimumAmount": "Minimum Amount", "minimumQuantity": "Minimum Quantity", "freeShippingCombinations": "Select which other discounts can be combined with this free shipping reward.", "editFreeShippingReward": "Edit Free Shipping Reward", "freeShippingReward": "Free Shipping Reward", "combinations": "Combinations", "deleteConfirmation": {"title": "Delete Reward Confirmation", "message": "Are you sure you want to delete the reward \"{{rewardTitle}}\"? This action cannot be undone."}, "validation": {"titleRequired": "Reward title is required", "valueRequired": "Reward value is required", "valueInvalid": "Reward value must be a positive number"}, "allRewardsAdded": "All available reward types have already been added.", "productDiscounts": "Product Discounts", "orderDiscounts": "Order Discounts", "shippingDiscounts": "Shipping Discounts", "storeCredits": "Store Credits", "diffpointDescription": "Different point values based on VIP tiers", "pointForSpent": "points for every $", "spent": "spent"}, "status": {"active": "Active", "inactive": "Inactive", "activate": "Activate", "deactivate": "Deactivate", "statusMessage": "{{program}} program is currently {{status}}."}, "vip": {"entryMethod": {"title": "Entry method", "description": "Define entry method for unlocking VIP Tiers", "pointsEarned": "Points earned", "amountSpent": "Amount spent", "ordersCount": "Orders count", "edit": "Edit", "unlockMethod": "Select method for customers to unlock"}, "validation": {"title": "Tier validation", "description": "Select when the tier entry method is counted for members to enter a higher VIP tier", "immediately": "Immediately", "daysAfterPaid": "days after paid", "cancel": "Cancel", "save": "Save"}, "tiers": {"title": "Tiers", "description": "Set ways for your customers to earn rewards", "addNew": "Add new", "deleteConfirmation": {"title": "Delete VIP Tier", "message": "Are you sure you want to delete the {{tierName}} tier? This action cannot be undone.", "cancel": "Cancel", "delete": "Delete"}, "deletingMessage": "Deleting VIP tier..."}, "settings": {"title": "Settings", "expiration": {"title": "Expiration", "description": "How long you will allow your customers to take to achieve a VIP Tier", "lifetime": "Lifetime", "periodOfTime": "period of time", "days": "days", "cancel": "Cancel", "save": "Save"}}, "newTier": {"title": "Create new tier", "backAction": "Loyalties", "tierName": "Tier name", "entryGoal": "Entry goal", "spentSinceStartDate": "spent since start date", "rewards": {"title": "Rewards to unlock", "addNew": "Add new", "description": "Rewards are automatically unlocked upon reaching this VIP tier and will renew at the end of each membership period.", "addedRewards": "Added Rewards:"}, "save": "Save", "saving": "Saving...", "validation": {"tierNameRequired": "Tier name is required", "entryGoalRequired": "Entry goal is required", "entryGoalInvalid": "Entry goal must be a valid positive number"}}, "editTier": {"title": "Edit VIP tier", "backAction": "Loyalties", "tierName": "Tier name", "entryGoal": "Entry goal", "spentSinceStartDate": "spent since start date", "rewards": {"title": "Rewards to unlock", "addNew": "Add new", "description": "Rewards are automatically unlocked upon reaching this VIP tier and will renew at the end of each membership period.", "addedRewards": "Added Rewards:"}, "save": "Save", "saving": "Saving..."}, "toastMessages": {"entryMethodUpdated": "Entry method updated successfully", "validationUpdated": "Validation settings updated successfully", "expirationUpdated": "Expiration settings updated successfully", "settingsUpdated": "Settings updated successfully", "tierCreated": "VIP tier created successfully", "tierCreateFailed": "Failed to create VIP tier", "tierUpdated": "VIP tier updated successfully", "tierUpdateFailed": "Failed to update VIP tier", "rewardAdded": "{{title}} reward added successfully", "rewardUpdated": "{{title}} reward updated successfully", "rewardDeleted": "<PERSON><PERSON> deleted successfully", "rewardNotImplemented": "{{type}} rewards are not yet implemented"}}, "points": {"save": "Save", "saving": "Saving...", "success": "Success", "formError": "Please fix the errors in the form", "settingsSaved": "Setting<PERSON> saved successfully", "programBranding": {"title": "Program Branding", "programName": {"title": "Program Name", "description": "Customize your loyalty program name to match your brand. It will be displayed on the loyalty widget.", "helpText": "Enter your program name.", "error": "Program name is required"}, "pointCurrency": {"title": "Point Currency Name", "description": "Customize your points currency name.", "singularHelpText": "Singular form. Example: Hi Point", "pluralHelpText": "Plural form. Example: Hi Points (optional)", "error": "Point name is required"}}, "pointsRule": {"title": "Points Rule", "rewardValue": {"title": "Points Reward Value", "description": "Conversion rate when earning points.", "earnText": "Customers earn {{points}} points for every NT$ {{amount}} spent", "convertByPercentage": "Convert by percentage of purchase", "roundingOptions": {"round": "Round to nearest integer", "floor": "Round down (floor)", "ceiling": "Round up (ceiling)"}, "currencyError": "Currency amount is required", "currencyInvalidError": "Currency amount must be a positive number", "pointsError": "Points value is required", "pointsInvalidError": "Points value must be a positive number"}, "redemptionValue": {"title": "Points Redemption Value", "description": "Value of points when redeemed.", "equalsText": "{{points}} points equals {{value}} NT$ discount", "error": "Points redemption value is required", "invalidError": "Points redemption value must be a positive number"}, "maximumRedeem": {"title": "Maximum Redeem", "description": "Maximum percentage of order total that can be paid with points.", "ofTotal": "% of total", "helpText": "Points can be used to discount up to {{percentage}}% of the order total", "error": "Maximum redeem percentage is required", "invalidError": "Maximum redeem percentage must be between 1 and 100"}, "minimumPurchase": {"title": "Minimum Purchase", "description": "Minimum purchase amount required to use points.", "none": "None", "fixed": "Fixed amount", "currency": "NT$TWD", "helpText": "Customers must spend at least ${{amount}} TWD to use points discounts.", "error": "Minimum purchase amount is required", "invalidError": "Minimum purchase amount must be a positive number"}}, "orderTab": {"pointsReward": "Points Reward", "orderTotal": {"title": "Order Total", "description": "Choose which components of the order total to include when calculating points.", "productTotal": "Product total", "includeShipping": "Include shipping", "includeTaxes": "Include taxes"}, "pointsIssueDate": {"title": "Points Issue Date", "description": "Determine when points are issued to customers after a purchase.", "issuePointsReward": "Issue Points Reward", "immediately": "Immediately", "days": "days", "afterOrderIs": "after order is", "rewardPointsIssued": "Reward points will be issued based on the selected criteria."}, "orderStatus": {"paidAndFulfilled": "Paid and fulfilled", "paid": "Paid"}, "orderRewardedPointsRefund": {"title": "Order Rewarded Points Refund", "description": "Determine how points are handled when an order is refunded.", "proportionally": "Proportionally", "proportionallyHelpText": "Deduct points proportionally to the refunded amount.", "full": "Full", "fullHelpText": "Deduct all points earned from the order regardless of refund amount.", "none": "None", "noneHelpText": "Do not deduct any points when an order is refunded."}, "redeemedPointsRefund": {"title": "Redeemed Points Refund", "description": "Determine how redeemed points are handled when an order is refunded.", "proportionally": "Proportionally", "proportionallyHelpText": "Return points proportionally to the refunded amount.", "none": "None", "noneHelpText": "Do not return any redeemed points when an order is refunded."}}}, "storeCredit": {"name": {"title": " Store credit name", "description": "Customize the name of your store credit.", "singularHelpText": "Singular term. e.g., Loyalty credit", "pluralHelpText": "Plural form. e.g., Loyalty credits (optional)"}, "rewardRate": {"title": "Store credit reward rate", "description": "Automatically reward store credit based on the order total. For example, 1.5% means earning 1.5 store credits for every $100 spent.", "editButton": "Edit in reward"}, "editModal": {"title": "Store credit", "description": "Customers earn store credits by placing an order, or subscribing a new membership", "placeOrder": {"title": "Place an order", "description": "From 1 point for every $1 spent"}, "subscribeMembership": {"title": "Subscribe new membership", "description": "From 3% store credits"}}}}, "settings": {"title": "Settings", "apiKeys": "API Keys", "subtitle": "Manage your API keys to access application services.", "apiKey": "API Key", "securityWarning": "Your API key grants full access to the API. Keep it secure and never share it publicly.", "generateNewKey": "Generate New Key", "generating": "Generating...", "keyUpdated": "Your API key has been updated", "keyCopied": "Your API key has been copied to clipboard", "endpointCopied": "Endpoint has been copied to clipboard", "language": "Language", "languageDescription": "Choose your preferred language", "languageUpdated": "Language has been updated successfully", "endpointsTitle": "API Endpoints", "endpointsDescription": "Authorization method: Using Bearer Token authentication", "viewReference": "View API reference", "copyButton": "Copy", "endpoints": {"addMember": "Add New Member", "queryMember": "Query Member Basic Information", "updateMember": "Modify Member Basic Information", "cancelMembership": "Cancel Membership", "queryOffers": "Query Member's Available Offers", "addOrder": "Add In-Store Member Order"}}, "birthday": {"title": "<PERSON><PERSON><PERSON>te a birthday", "form": {"titleLabel": "Title", "titlePlaceholder": "Happy birthday!", "titleDescription": "Customers will see this in their cart and at checkout."}, "rewards": {"title": "Rewards", "maxLimit": "Maximum of 5 rewards.", "addNew": "Add new", "addRewardWarning": "Please add at least one reward to enable saving.", "displayInfo": "The rewards will be displayed on the widget.", "modalTitle": "Add new reward", "options": {"points": "Points", "storeCredit": "Store credit", "amountOff": "Amount off order", "freeShipping": "Free shipping discount"}}, "birthdaySection": {"title": "Birthday", "editableInfo": "Editable after saving"}, "genderOptions": {"title": "Gender options", "female": "Female", "male": "Male", "transgender": "Transgender", "nonBinary": "Non-binary", "preferNotToSay": "Prefer not to say", "editButton": "Edit in Customer profile"}, "eligibilityInfo": "Customers must enter their birthday on their account or the widget at least 24 hours in advance to be eligible for reward."}, "members": {"title": "Member Management", "error": {"loading": "An error occurred while loading data: ", "unknown": "Unknown error"}, "search": {"button": "Search", "types": {"email": "Email", "name": "Name"}, "placeholder": {"email": "Search by email...", "name": "Search by name..."}}, "table": {"name": "Name", "points": "Points", "vipTier": "VIP Tier", "orders": "Orders", "totalSpent": "Total Spent"}, "vipTiers": {"starter": "Starter", "vip": "VIP", "svip": "SVIP", "ssvip": "SSVIP"}, "details": {"backToList": "Back to List", "notFound": "Member Not Found", "notFoundMessage": "The member does not exist or has been deleted.", "errorPrefix": "Error: ", "viewInShopify": "View in Shopify", "stats": {"ordersCount": "Orders Count", "totalSpent": "Total spent", "lifetimeOrders": "Lifetime orders from Shopify", "lifetimeSpent": "Lifetime spent from Shopify"}, "timeline": {"title": "Timeline", "noEvents": "No timeline events available for this customer."}, "profile": {"contactInfo": "Contact Information", "phone": "Phone: ", "email": "Email: ", "address": "Address", "notSet": "Not set", "additionalInfo": "Additional Information", "gender": "Gender: ", "registerDate": "Register Date: ", "birthday": "Birthday", "edit": "Edit", "invalidDate": "Invalid date"}, "social": {"title": "Social platforms", "notConnected": "Not connected"}, "birthdayModal": {"title": "Edit Birthday", "save": "Save", "cancel": "Cancel", "label": "Birthday", "helpText": "Select a date from the calendar above"}}}, "completeProfile": {"pageTitle": "Complete your profile", "backAction": "Loyalties", "title": {"label": "Title", "helpText": "Customers will see this in their cart and at checkout."}, "rewards": {"title": "Rewards", "addNew": "Add new", "maxLimit": "Maximum of 5 rewards.", "pointsReward": "Points reward", "points": "points", "displayInfo": "The rewards will be displayed on the widget."}, "birthday": {"title": "Birthday", "editableInfo": "Editable after saving", "nonEditableInfo": "Non-editable after saving"}, "gender": {"title": "Gender options", "options": {"female": "Female", "male": "Male", "transgender": "Transgender", "nonBinary": "Non-binary", "preferNotToSay": "Prefer not to say"}, "editButton": "Edit in Customer profile"}, "eligibilityInfo": "Customers must enter their birthday on their account or the widget at least 24 hours in advance to be eligible for reward.", "status": {"title": "Status", "active": "Active", "on": "On", "pauseInfo": "Turn \"Off\" the switch to pause."}, "summary": {"title": "Summary", "userInput": "User can input their gender and birthday"}, "emailNotifications": {"title": "Email notifications", "warningMessage": "Click \"Edit\" to activate email notifications and inform your customers.", "completeProfile": "Complete your profile", "inactive": "Inactive"}, "icon": {"title": "Icon", "defaultIcon": "De<PERSON>ult <PERSON>", "customIcon": "Custom Icon", "upload": "Upload", "sizeInfo": "1:1 ratio (e.g. 60×60px) and less than 50KB png, jpg"}, "buttons": {"delete": "Delete", "save": "Save", "edit": "Edit"}, "learnMore": "Learn more about", "completingUserProfile": "completing user profile"}, "signup": {"pageTitle": "Sign up", "backAction": "Loyalties", "title": {"label": "Title", "helpText": "Customers will see this in their cart and at checkout."}, "rewards": {"title": "Rewards", "addNew": "Add new", "description": "Customers earn rewards by creating an account. (Maximum of 5 rewards.)"}, "waysToEarn": {"title": "Ways to Earn <PERSON>s", "description": "These are the ways customers can earn rewards when signing up."}, "status": {"title": "Status", "active": "Active", "inactive": "Inactive", "on": "On", "pauseInfo": "Turn \"Off\" the switch to pause."}, "buttons": {"delete": "Delete", "save": "Save"}, "toastMessages": {"saved": "Setting<PERSON> saved successfully", "error": "Failed to save settings"}}, "comon": {"from": "From", "basedOnDiffTier": "(based on VIP tiers)"}}